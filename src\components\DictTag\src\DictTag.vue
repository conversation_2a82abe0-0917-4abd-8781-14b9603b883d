<template>
  <div
    v-if="dictOptions.length > 0"
    class="dict-tag"
    :style="{
      display: 'inline-flex',
      gap: gutter,
      justifyContent: 'center',
      alignItems: 'center'
    }"
  >
    <el-tag
      v-for="dict in matchedDictOptions"
      :key="dict.value"
      :style="dict?.cssClass ? 'color: #fff' : ''"
      :type="dict?.colorType || null"
      :color="dict?.cssClass && isHexColor(dict?.cssClass) ? dict?.cssClass : ''"
      :disable-transitions="true"
      v-bind="$attrs"
    >
      <slot name="prefix" :dict="dict"></slot>
      {{ dict?.label }}
      <slot name="suffix" :dict="dict"></slot>
    </el-tag>
  </div>
</template>

<script lang="ts" setup>
import { computed, PropType } from 'vue'
import { isHexColor } from '@/utils/color'
import { DictDataType, getDictOptions } from '@/utils/dict'
import { isArray, isBoolean, isNumber, isString } from '@/utils/is'

defineOptions({ name: 'DictTag' })

const props = defineProps({
  type: {
    type: String as PropType<string>,
    required: true
  },
  value: {
    type: [String, Number, Boolean, Array],
    required: true
  },
  // 字符串分隔符 只有当 props.value 传入值为字符串时有效
  separator: {
    type: String as PropType<string>,
    default: ','
  },
  // 每个 tag 之间的间隔，默认为 5px，参考的 el-row 的 gutter
  gutter: {
    type: String as PropType<string>,
    default: '5px'
  }
})

const valueArr = computed(() => {
  // 1. 是 Number 类型和 Boolean 类型的情况
  if (isNumber(props.value) || isBoolean(props.value)) {
    return [String(props.value)]
  }
  // 2. 是字符串（进一步判断是否有包含分隔符号 -> props.separator ）
  else if (isString(props.value)) {
    return props.value.split(props.separator)
  }
  // 3. 数组
  else if (isArray(props.value)) {
    return props.value.map(String)
  }
  return []
})

const dictOptions = computed(() => {
  if (!props.type) {
    return []
  }
  // 解决自定义字典标签值为零时标签不渲染的问题
  if (props.value === undefined || props.value === null || props.value === '') {
    return []
  }
  return getDictOptions(props.type)
})

const matchedDictOptions = computed(() => {
  return dictOptions.value.filter((dict: DictDataType) => {
    if (valueArr.value.includes(dict.value)) {
      if (dict.colorType + '' === 'primary' || dict.colorType + '' === 'default') {
        dict.colorType = ''
      }
      return true
    }
    return false
  })
})
</script>
